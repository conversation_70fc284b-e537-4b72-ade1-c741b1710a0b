@if (has_nav_menu('primary_navigation'))
	@php($primary_navigation = wp_get_nav_menu_items(wp_get_nav_menu_name('primary_navigation')))
@endif

<header x-data="{ showSearch: false }">
	<div class="container flex justify-between items-center py-6">
		<img src="{{Vite::asset('resources/images/logo.svg')}}" alt="Servochem Logo" />
		<div class="flex items-center gap-14">
			@foreach ($primary_navigation as $item)
				<a href="{{ $item->url }}" class="font-semibold hover:text-red transition-all no-underline">{{ $item->title }}</a>
			@endforeach
			<a @click.prevent="showSearch = !showSearch" class="group" href="#">
				<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
					<path class="fill-black group-hover:fill-red transition-all" fill-rule="evenodd" clip-rule="evenodd" d="M4.66547 4.66547C6.2127 3.11825 8.31119 2.24902 10.4993 2.24902C12.6874 2.24902 14.7859 3.11825 16.3331 4.66547C17.8804 6.2127 18.7496 8.31119 18.7496 10.4993C18.7496 12.436 18.0686 14.3025 16.8393 15.7787L21.5301 20.4695C21.823 20.7624 21.823 21.2372 21.5301 21.5301C21.2372 21.823 20.7624 21.823 20.4695 21.5301L15.7787 16.8393C14.3025 18.0686 12.436 18.7496 10.4993 18.7496C8.3112 18.7496 6.2127 17.8804 4.66547 16.3331C3.11825 14.7859 2.24902 12.6874 2.24902 10.4993C2.24902 8.31119 3.11825 6.2127 4.66547 4.66547ZM10.4993 3.74902C8.70902 3.74902 6.99206 4.46021 5.72614 5.72614C4.46021 6.99206 3.74902 8.70902 3.74902 10.4993C3.74902 12.2896 4.46021 14.0066 5.72614 15.2725C6.99206 16.5384 8.70902 17.2496 10.4993 17.2496C12.2896 17.2496 14.0066 16.5384 15.2725 15.2725C16.5384 14.0066 17.2496 12.2896 17.2496 10.4993C17.2496 8.70902 16.5384 6.99206 15.2725 5.72614C14.0066 4.46021 12.2896 3.74902 10.4993 3.74902Z"/>
				</svg>
			</a>
		</div>
	</div>
	<div x-show="showSearch" class="fixed inset-0 z-20 bg-white/80">
		<div class="container h-full flex justify-center items-center">
			Search
		</div>
	</div>
</header>

