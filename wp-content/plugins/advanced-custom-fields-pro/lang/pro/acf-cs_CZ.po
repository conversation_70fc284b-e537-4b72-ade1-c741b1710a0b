msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields PRO\n"
"Report-Msgid-Bugs-To: https://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2023-04-18 07:25+0000\n"
"PO-Revision-Date: 2023-04-24 13:31+0100\n"
"Last-Translator: WP Engine <<EMAIL>>\n"
"Language-Team: WP Engine <<EMAIL>>\n"
"Language: cs_CZ\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1) ? 0 : (n>=2 && n<=4) ? 1 : 2;\n"
"X-Generator: Poedit 3.2.2\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/blocks.php:170
#, fuzzy
#| msgid "%s value is required"
msgid "Block type name is required."
msgstr "%s hodnota je vyžadována"

#. translators: The name of the block type
#: pro/blocks.php:178
msgid "Block type \"%s\" is already registered."
msgstr ""

#: pro/blocks.php:726
msgid "Switch to Edit"
msgstr ""

#: pro/blocks.php:727
msgid "Switch to Preview"
msgstr ""

#: pro/blocks.php:728
msgid "Change content alignment"
msgstr ""

#. translators: %s: Block type title
#: pro/blocks.php:731
#, fuzzy
#| msgid "Settings"
msgid "%s settings"
msgstr "Nastavení"

#: pro/blocks.php:936
msgid "This block contains no editable fields."
msgstr ""

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:942
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""

#: pro/options-page.php:47
msgid "Options"
msgstr "Konfigurace"

#: pro/options-page.php:77, pro/fields/class-acf-field-gallery.php:527
msgid "Update"
msgstr "Aktualizace"

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "Nastavení aktualizováno"

#: pro/updates.php:99
#, fuzzy
#| msgid ""
#| "To enable updates, please enter your license key on the <a "
#| "href=\"%s\">Updates</a> page. If you don't have a licence key, please see "
#| "<a href=\"%s\">details & pricing</a>."
msgid ""
"To enable updates, please enter your license key on the <a "
"href=\"%1$s\">Updates</a> page. If you don't have a licence key, please see "
"<a href=\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"Chcete-li povolit aktualizace, zadejte prosím licenční klíč na stránce <a "
"href=\"%s\">Aktualizace</a>. Pokud nemáte licenční klíč, přečtěte si <a "
"href=\"%s\">podrobnosti a ceny</a>."

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr ""

#: pro/updates.php:187
#, fuzzy
#| msgid "<b>Error</b>. Could not connect to update server"
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr "<b>Chyba</b>. Nelze se připojit k serveru a aktualizovat"

#: pro/updates.php:279
msgid "Check Again"
msgstr "Zkontrolujte znovu"

#: pro/updates.php:593
#, fuzzy
#| msgid "<b>Error</b>. Could not connect to update server"
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr "<b>Chyba</b>. Nelze se připojit k serveru a aktualizovat"

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "Publikovat"

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Nebyly nalezeny žádné vlastní skupiny polí. <a href=\"%s\">Vytvořit vlastní "
"skupinu polí</a>"

#: pro/admin/admin-options-page.php:309
msgid "Edit field group"
msgstr "Editovat skupinu polí"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Chyba</b>. Nelze se připojit k serveru a aktualizovat"

#: pro/admin/admin-updates.php:122,
#: pro/admin/views/html-settings-updates.php:12
msgid "Updates"
msgstr "Aktualizace"

#: pro/admin/admin-updates.php:212
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""

#: pro/admin/admin-updates.php:199
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Klonovat"

#: pro/fields/class-acf-field-clone.php:27,
#: pro/fields/class-acf-field-repeater.php:31
msgid ""
"Allows you to select and display existing fields. It does not duplicate any "
"fields in the database, but loads and displays the selected fields at run-"
"time. The Clone field can either replace itself with the selected fields or "
"display the selected fields as a group of subfields."
msgstr ""

#: pro/fields/class-acf-field-clone.php:818,
#: pro/fields/class-acf-field-flexible-content.php:78
msgid "Fields"
msgstr "Pole"

#: pro/fields/class-acf-field-clone.php:819
msgid "Select one or more fields you wish to clone"
msgstr "Vyberte jedno nebo více polí, které chcete klonovat"

#: pro/fields/class-acf-field-clone.php:838
msgid "Display"
msgstr "Zobrazovat"

#: pro/fields/class-acf-field-clone.php:839
msgid "Specify the style used to render the clone field"
msgstr "Určení stylu použitého pro vykreslení klonovaných polí"

#: pro/fields/class-acf-field-clone.php:844
msgid "Group (displays selected fields in a group within this field)"
msgstr "Skupina (zobrazuje vybrané pole ve skupině v tomto poli)"

#: pro/fields/class-acf-field-clone.php:845
msgid "Seamless (replaces this field with selected fields)"
msgstr "Bezešvé (nahradí toto pole vybranými poli)"

#: pro/fields/class-acf-field-clone.php:854,
#: pro/fields/class-acf-field-flexible-content.php:558,
#: pro/fields/class-acf-field-flexible-content.php:616,
#: pro/fields/class-acf-field-repeater.php:177
msgid "Layout"
msgstr "Typ zobrazení"

#: pro/fields/class-acf-field-clone.php:855
msgid "Specify the style used to render the selected fields"
msgstr "Určení stylu použitého pro vykreslení vybraných polí"

#: pro/fields/class-acf-field-clone.php:860,
#: pro/fields/class-acf-field-flexible-content.php:629,
#: pro/fields/class-acf-field-repeater.php:185,
#: pro/locations/class-acf-location-block.php:22
msgid "Block"
msgstr "Blok"

#: pro/fields/class-acf-field-clone.php:861,
#: pro/fields/class-acf-field-flexible-content.php:628,
#: pro/fields/class-acf-field-repeater.php:184
msgid "Table"
msgstr "Tabulka"

#: pro/fields/class-acf-field-clone.php:862,
#: pro/fields/class-acf-field-flexible-content.php:630,
#: pro/fields/class-acf-field-repeater.php:186
msgid "Row"
msgstr "Řádek"

#: pro/fields/class-acf-field-clone.php:868
msgid "Labels will be displayed as %s"
msgstr "Štítky budou zobrazeny jako %s"

#: pro/fields/class-acf-field-clone.php:873
msgid "Prefix Field Labels"
msgstr "Prefix štítku pole"

#: pro/fields/class-acf-field-clone.php:883
msgid "Values will be saved as %s"
msgstr "Hodnoty budou uloženy jako %s"

#: pro/fields/class-acf-field-clone.php:888
msgid "Prefix Field Names"
msgstr "Prefix jména pole"

#: pro/fields/class-acf-field-clone.php:1005
msgid "Unknown field"
msgstr "Neznámé pole"

#: pro/fields/class-acf-field-clone.php:1009
msgid "(no title)"
msgstr "(bez názvu)"

#: pro/fields/class-acf-field-clone.php:1042
msgid "Unknown field group"
msgstr "Skupina neznámých polí"

#: pro/fields/class-acf-field-clone.php:1046
msgid "All fields from %s field group"
msgstr "Všechna pole z skupiny polí %s"

#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "Flexibilní obsah"

#: pro/fields/class-acf-field-flexible-content.php:27
msgid ""
"Allows you to define, create and manage content with total control by "
"creating layouts that contain subfields that content editors can choose from."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:27
msgid "We do not recommend using this field in ACF Blocks."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:36,
#: pro/fields/class-acf-field-repeater.php:103,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Add Row"
msgstr "Přidat řádek"

#: pro/fields/class-acf-field-flexible-content.php:76,
#: pro/fields/class-acf-field-flexible-content.php:943,
#: pro/fields/class-acf-field-flexible-content.php:1022
msgid "layout"
msgid_plural "layouts"
msgstr[0] "typ zobrazení"
msgstr[1] "typ zobrazení"
msgstr[2] "typ zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:77
msgid "layouts"
msgstr "typy zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:81,
#: pro/fields/class-acf-field-flexible-content.php:942,
#: pro/fields/class-acf-field-flexible-content.php:1021
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Toto pole vyžaduje alespoň {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Toto pole má limit {max}{label}  {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} dostupný (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:86
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} povinný (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:89
msgid "Flexible Content requires at least 1 layout"
msgstr "Flexibilní obsah vyžaduje minimálně jedno rozložení obsahu"

#: pro/fields/class-acf-field-flexible-content.php:282
msgid "Click the \"%s\" button below to start creating your layout"
msgstr ""
"Klikněte na tlačítko \"%s\" níže pro vytvoření vlastního typu zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:420,
#: pro/fields/class-acf-repeater-table.php:366
msgid "Drag to reorder"
msgstr "Přetažením změníte pořadí"

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "Přidat typ zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:424
#, fuzzy
#| msgid "Duplicate Layout"
msgid "Duplicate layout"
msgstr "Duplikovat typ zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:425
msgid "Remove layout"
msgstr "Odstranit typ zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:426,
#: pro/fields/class-acf-repeater-table.php:382
msgid "Click to toggle"
msgstr "Klikněte pro přepnutí"

#: pro/fields/class-acf-field-flexible-content.php:562
msgid "Delete Layout"
msgstr "Smazat typ zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:563
msgid "Duplicate Layout"
msgstr "Duplikovat typ zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:564
msgid "Add New Layout"
msgstr "Přidat nový typ zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:564
#, fuzzy
#| msgid "Add layout"
msgid "Add Layout"
msgstr "Přidat typ zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:593
msgid "Label"
msgstr "Štítek"

#: pro/fields/class-acf-field-flexible-content.php:609
msgid "Name"
msgstr "Jméno"

#: pro/fields/class-acf-field-flexible-content.php:647
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:662
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:705
msgid "Minimum Layouts"
msgstr "Minimální rozložení"

#: pro/fields/class-acf-field-flexible-content.php:716
msgid "Maximum Layouts"
msgstr "Maximální rozložení"

#: pro/fields/class-acf-field-flexible-content.php:727,
#: pro/fields/class-acf-field-repeater.php:293
msgid "Button Label"
msgstr "Nápis tlačítka"

#: pro/fields/class-acf-field-flexible-content.php:1710,
#: pro/fields/class-acf-field-repeater.php:918
msgid "%s must be of type array or null."
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:1721
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: pro/fields/class-acf-field-flexible-content.php:1737
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "Galerie"

#: pro/fields/class-acf-field-gallery.php:27
msgid ""
"An interactive interface for managing a collection of attachments, such as "
"images."
msgstr ""

#: pro/fields/class-acf-field-gallery.php:77
msgid "Add Image to Gallery"
msgstr "Přidat obrázek do galerie"

#: pro/fields/class-acf-field-gallery.php:78
msgid "Maximum selection reached"
msgstr "Maximální výběr dosažen"

#: pro/fields/class-acf-field-gallery.php:324
msgid "Length"
msgstr "Délka"

#: pro/fields/class-acf-field-gallery.php:339
msgid "Edit"
msgstr "Upravit"

#: pro/fields/class-acf-field-gallery.php:340,
#: pro/fields/class-acf-field-gallery.php:495
msgid "Remove"
msgstr "Odstranit"

#: pro/fields/class-acf-field-gallery.php:356
msgid "Title"
msgstr "Název"

#: pro/fields/class-acf-field-gallery.php:368
msgid "Caption"
msgstr "Popisek"

#: pro/fields/class-acf-field-gallery.php:380
msgid "Alt Text"
msgstr "Alternativní text"

#: pro/fields/class-acf-field-gallery.php:392
msgid "Description"
msgstr "Popis"

#: pro/fields/class-acf-field-gallery.php:504
msgid "Add to gallery"
msgstr "Přidat do galerie"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Bulk actions"
msgstr "Hromadné akce"

#: pro/fields/class-acf-field-gallery.php:509
msgid "Sort by date uploaded"
msgstr "Řadit dle data nahrání"

#: pro/fields/class-acf-field-gallery.php:510
msgid "Sort by date modified"
msgstr "Řadit dle data změny"

#: pro/fields/class-acf-field-gallery.php:511
msgid "Sort by title"
msgstr "Řadit dle názvu"

#: pro/fields/class-acf-field-gallery.php:512
msgid "Reverse current order"
msgstr "Převrátit aktuální pořadí"

#: pro/fields/class-acf-field-gallery.php:524
msgid "Close"
msgstr "Zavřít"

#: pro/fields/class-acf-field-gallery.php:556
msgid "Return Format"
msgstr "Formát návratové hodnoty"

#: pro/fields/class-acf-field-gallery.php:562
msgid "Image Array"
msgstr "Pole obrázku"

#: pro/fields/class-acf-field-gallery.php:563
msgid "Image URL"
msgstr "Adresa obrázku"

#: pro/fields/class-acf-field-gallery.php:564
msgid "Image ID"
msgstr "ID obrázku"

#: pro/fields/class-acf-field-gallery.php:572
msgid "Library"
msgstr "Knihovna"

#: pro/fields/class-acf-field-gallery.php:573
msgid "Limit the media library choice"
msgstr "Omezit výběr knihovny médií"

#: pro/fields/class-acf-field-gallery.php:578,
#: pro/locations/class-acf-location-block.php:66
msgid "All"
msgstr "Vše"

#: pro/fields/class-acf-field-gallery.php:579
msgid "Uploaded to post"
msgstr "Nahráno k příspěvku"

#: pro/fields/class-acf-field-gallery.php:615
msgid "Minimum Selection"
msgstr "Minimální výběr"

#: pro/fields/class-acf-field-gallery.php:625
msgid "Maximum Selection"
msgstr "Maximální výběr"

#: pro/fields/class-acf-field-gallery.php:635
msgid "Minimum"
msgstr "Minimum"

#: pro/fields/class-acf-field-gallery.php:636,
#: pro/fields/class-acf-field-gallery.php:672
msgid "Restrict which images can be uploaded"
msgstr "Omezte, které typy obrázků je možné nahrát"

#: pro/fields/class-acf-field-gallery.php:639,
#: pro/fields/class-acf-field-gallery.php:675
msgid "Width"
msgstr "Šířka"

#: pro/fields/class-acf-field-gallery.php:650,
#: pro/fields/class-acf-field-gallery.php:686
msgid "Height"
msgstr "Výška"

#: pro/fields/class-acf-field-gallery.php:662,
#: pro/fields/class-acf-field-gallery.php:698
msgid "File size"
msgstr "Velikost souboru"

#: pro/fields/class-acf-field-gallery.php:671
msgid "Maximum"
msgstr "Maximum"

#: pro/fields/class-acf-field-gallery.php:707
msgid "Allowed file types"
msgstr "Povolené typy souborů"

#: pro/fields/class-acf-field-gallery.php:708
msgid "Comma separated list. Leave blank for all types"
msgstr "Seznam oddělený čárkami. Nechte prázdné pro povolení všech typů"

#: pro/fields/class-acf-field-gallery.php:727
msgid "Insert"
msgstr "Vložit"

#: pro/fields/class-acf-field-gallery.php:728
msgid "Specify where new attachments are added"
msgstr "Určete, kde budou přidány nové přílohy"

#: pro/fields/class-acf-field-gallery.php:732
msgid "Append to the end"
msgstr "Přidat na konec"

#: pro/fields/class-acf-field-gallery.php:733
msgid "Prepend to the beginning"
msgstr "Přidat na začátek"

#: pro/fields/class-acf-field-gallery.php:741
msgid "Preview Size"
msgstr "Velikost náhledu"

#: pro/fields/class-acf-field-gallery.php:844
#, fuzzy
#| msgid "%s requires at least %s selection"
#| msgid_plural "%s requires at least %s selections"
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%s vyžaduje alespoň %s volbu"
msgstr[1] "%s vyžaduje alespoň %s volby"
msgstr[2] "%s vyžaduje alespoň %s voleb"

#: pro/fields/class-acf-field-repeater.php:29
msgid "Repeater"
msgstr "Opakovač"

#: pro/fields/class-acf-field-repeater.php:66,
#: pro/fields/class-acf-field-repeater.php:463
#, fuzzy
#| msgid "Minimum rows reached ({min} rows)"
msgid "Minimum rows not reached ({min} rows)"
msgstr "Minimální počet řádků dosažen ({min} řádků)"

#: pro/fields/class-acf-field-repeater.php:67
msgid "Maximum rows reached ({max} rows)"
msgstr "Maximální počet řádků dosažen ({max} řádků)"

#: pro/fields/class-acf-field-repeater.php:68
msgid "Error loading page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:69
msgid "Order will be assigned upon save"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:162
msgid "Sub Fields"
msgstr "Podřazená pole"

#: pro/fields/class-acf-field-repeater.php:195
#, fuzzy
#| msgid "Position"
msgid "Pagination"
msgstr "Pozice"

#: pro/fields/class-acf-field-repeater.php:196
msgid "Useful for fields with a large number of rows."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:207
#, fuzzy
#| msgid "Posts Page"
msgid "Rows Per Page"
msgstr "Stránka příspěvku"

#: pro/fields/class-acf-field-repeater.php:208
#, fuzzy
#| msgid "Select the taxonomy to be displayed"
msgid "Set the number of rows to be displayed on a page."
msgstr "Zvolit zobrazovanou taxonomii"

#: pro/fields/class-acf-field-repeater.php:240
msgid "Minimum Rows"
msgstr "Minimum řádků"

#: pro/fields/class-acf-field-repeater.php:251
msgid "Maximum Rows"
msgstr "Maximum řádků"

#: pro/fields/class-acf-field-repeater.php:281
msgid "Collapsed"
msgstr "Sbaleno"

#: pro/fields/class-acf-field-repeater.php:282
msgid "Select a sub field to show when row is collapsed"
msgstr "Zvolte dílčí pole, které se zobrazí při sbalení řádku"

#: pro/fields/class-acf-field-repeater.php:1045
msgid "Invalid nonce."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1060
msgid "Invalid field key or name."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:1069
msgid "There was an error retrieving the field."
msgstr ""

#: pro/fields/class-acf-repeater-table.php:369
#, fuzzy
#| msgid "Drag to reorder"
msgid "Click to reorder"
msgstr "Přetažením změníte pořadí"

#: pro/fields/class-acf-repeater-table.php:402
msgid "Add row"
msgstr "Přidat řádek"

#: pro/fields/class-acf-repeater-table.php:403
#, fuzzy
#| msgid "Duplicate"
msgid "Duplicate row"
msgstr "Duplikovat"

#: pro/fields/class-acf-repeater-table.php:404
msgid "Remove row"
msgstr "Odebrat řádek"

#: pro/fields/class-acf-repeater-table.php:448,
#: pro/fields/class-acf-repeater-table.php:465,
#: pro/fields/class-acf-repeater-table.php:466
#, fuzzy
#| msgid "Parent Page"
msgid "Current Page"
msgstr "Rodičovská stránka"

#: pro/fields/class-acf-repeater-table.php:456,
#: pro/fields/class-acf-repeater-table.php:457
#, fuzzy
#| msgid "Front Page"
msgid "First Page"
msgstr "Hlavní stránka"

#: pro/fields/class-acf-repeater-table.php:460,
#: pro/fields/class-acf-repeater-table.php:461
#, fuzzy
#| msgid "Posts Page"
msgid "Previous Page"
msgstr "Stránka příspěvku"

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:470
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:477,
#: pro/fields/class-acf-repeater-table.php:478
#, fuzzy
#| msgid "Parent Page"
msgid "Next Page"
msgstr "Rodičovská stránka"

#: pro/fields/class-acf-repeater-table.php:481,
#: pro/fields/class-acf-repeater-table.php:482
#, fuzzy
#| msgid "Posts Page"
msgid "Last Page"
msgstr "Stránka příspěvku"

#: pro/locations/class-acf-location-block.php:71
#, fuzzy
#| msgid "No options pages exist"
msgid "No block types exist"
msgstr "Neexistuje stránka nastavení"

#: pro/locations/class-acf-location-options-page.php:22
msgid "Options Page"
msgstr "Stránka konfigurace"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Neexistuje stránka nastavení"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Deaktivujte licenci"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Aktivujte licenci"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Informace o licenci"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Chcete-li povolit aktualizace, zadejte prosím licenční klíč. Pokud nemáte "
"licenční klíč, přečtěte si <a href=\"%s\">podrobnosti a ceny</a>."

#: pro/admin/views/html-settings-updates.php:37
msgid "License Key"
msgstr "Licenční klíč"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr ""

#: pro/admin/views/html-settings-updates.php:29
#, fuzzy
#| msgid "Activation Code"
msgid "Retry Activation"
msgstr "Aktivační kód"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Aktualizovat informace"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Současná verze"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Nejnovější verze"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Aktualizace je dostupná"

#: pro/admin/views/html-settings-updates.php:91
msgid "No"
msgstr "Ne"

#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "Ano"

#: pro/admin/views/html-settings-updates.php:98
msgid "Upgrade Notice"
msgstr "Upozornění na aktualizaci"

#: pro/admin/views/html-settings-updates.php:126
msgid "Check For Updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:121
#, fuzzy
#| msgid "Please enter your license key above to unlock updates"
msgid "Enter your license key to unlock updates"
msgstr "Pro odemčení aktualizací zadejte prosím výše svůj licenční klíč"

#: pro/admin/views/html-settings-updates.php:119
msgid "Update Plugin"
msgstr "Aktualizovat plugin"

#: pro/admin/views/html-settings-updates.php:117
#, fuzzy
#| msgid "Please enter your license key above to unlock updates"
msgid "Please reactivate your license to unlock updates"
msgstr "Pro odemčení aktualizací zadejte prosím výše svůj licenční klíč"
